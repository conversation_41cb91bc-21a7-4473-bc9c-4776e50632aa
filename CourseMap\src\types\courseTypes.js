// Data models based on your client's specifications

/**
 * Represents a point on the map (tee, basket, or waypoint)
 */
export class Point {
  constructor(longitude, latitude) {
    this.type = 'Point';
    this.coordinates = [longitude, latitude];
  }

  get longitude() {
    return this.coordinates[0];
  }

  get latitude() {
    return this.coordinates[1];
  }
}

/**
 * Represents a line string (fairway or walking path)
 */
export class LineString {
  constructor(coordinates = []) {
    this.type = 'LineString';
    this.coordinates = coordinates; // Array of [longitude, latitude] pairs
  }

  addPoint(longitude, latitude) {
    this.coordinates.push([longitude, latitude]);
  }

  getPoints() {
    return this.coordinates.map(coord => new Point(coord[0], coord[1]));
  }
}

/**
 * Represents a golf course hole
 */
export class CourseMapHole {
  constructor({
    id,
    courseId,
    userId,
    courseMapId = null,
    holeNumber,
    holePath = new LineString(),
    teeLocation = null,
    basketLocation = null,
    length = null,
    createdAt = new Date()
  }) {
    this.id = id;
    this.courseId = courseId;
    this.userId = userId;
    this.courseMapId = courseMapId;
    this.holeNumber = holeNumber;
    this.holePath = holePath;
    this.teeLocation = teeLocation;
    this.basketLocation = basketLocation;
    this.length = length;
    this.createdAt = createdAt;
  }

  // Convert to plain object for storage
  toJSON() {
    return {
      id: this.id,
      courseId: this.courseId,
      userId: this.userId,
      courseMapId: this.courseMapId,
      holeNumber: this.holeNumber,
      holePath: this.holePath,
      teeLocation: this.teeLocation,
      basketLocation: this.basketLocation,
      length: this.length,
      createdAt: this.createdAt.toISOString()
    };
  }

  // Create from plain object
  static fromJSON(data) {
    return new CourseMapHole({
      ...data,
      holePath: new LineString(data.holePath?.coordinates || []),
      teeLocation: data.teeLocation ? new Point(data.teeLocation.coordinates[0], data.teeLocation.coordinates[1]) : null,
      basketLocation: data.basketLocation ? new Point(data.basketLocation.coordinates[0], data.basketLocation.coordinates[1]) : null,
      createdAt: new Date(data.createdAt)
    });
  }
}

/**
 * Represents a walking path between holes
 */
export class CourseMapWalkingPath {
  constructor({
    id,
    courseId,
    userId,
    courseMapId = null,
    fromHole,
    toHole,
    path = new LineString(),
    length = null,
    createdAt = new Date()
  }) {
    this.id = id;
    this.courseId = courseId;
    this.userId = userId;
    this.courseMapId = courseMapId;
    this.fromHole = fromHole;
    this.toHole = toHole;
    this.path = path;
    this.length = length;
    this.createdAt = createdAt;
  }

  // Convert to plain object for storage
  toJSON() {
    return {
      id: this.id,
      courseId: this.courseId,
      userId: this.userId,
      courseMapId: this.courseMapId,
      fromHole: this.fromHole,
      toHole: this.toHole,
      path: this.path,
      length: this.length,
      createdAt: this.createdAt.toISOString()
    };
  }

  // Create from plain object
  static fromJSON(data) {
    return new CourseMapWalkingPath({
      ...data,
      path: new LineString(data.path?.coordinates || []),
      createdAt: new Date(data.createdAt)
    });
  }
}

// App modes
export const APP_MODES = {
  CREATE_HOLE: 'CREATE_HOLE',
  CREATE_WALKING_PATH: 'CREATE_WALKING_PATH',
  VIEW_SAVED: 'VIEW_SAVED',
  IDLE: 'IDLE'
};

// Drawing states for holes
export const HOLE_DRAWING_STATE = {
  PLACING_TEE: 'PLACING_TEE',
  DRAWING_FAIRWAY: 'DRAWING_FAIRWAY',
  PLACING_BASKET: 'PLACING_BASKET',
  COMPLETE: 'COMPLETE'
};
